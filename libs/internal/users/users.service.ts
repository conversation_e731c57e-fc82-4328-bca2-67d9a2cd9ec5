import { Injectable } from '@nestjs/common';
import { Auth<PERSON>rovider, User } from './entities/user.entity';
import { UserRepository } from './repositories/user.repository';
import { ApiError } from '../../common/api-errors';
import { GRPC_EXCEPTION, USER_NOT_FOUND_ERROR } from '../../common/api-errors/errors';
import { EntityManager, Loaded } from '@mikro-orm/core';
import { TelegramUserDTO } from './dto/telegram-user.dto';
import { UserManagedWalletRepository } from './repositories/user-managed-wallet.repository';
import { ChainType, UserManagedWallet } from './entities/user-managed-wallet.entity';
import { WalletService } from '../wallet/wallet.service';
import { getEvmBalance, getSolanaBalance, getTronBalance } from './wallet.utils';
import { UserPerpetualStatusRepository } from './repositories/user-perpetual-status.repository';
import { UserPerpetualStatusDTO } from './dto/perpetual-status.dto';
import { PerpetualAgentRepository } from './repositories/perpetual-agent.repository';
import { PerpetualAgentDto, UpdatePerpetualAgentDto } from './dto/perpetual-agent.dto';
import { GetUserInfoRequest, GetUserReferrerCodeResponse, UserInfoResponse } from '@protogen/user/v1/user_info';
import { RpcException } from '@nestjs/microservices';
import {
    ChainType as RequestChainType,
    SignUserTransactionEvmRequest,
    SignUserTransactionEvmResponse,
} from '@protogen/user/v1/signing';
import { parseEther, Transaction } from 'ethers';
import { UserDeviceRepository } from './repositories/user-device.repository';
import { ActivityLogRepository } from './repositories/activity-log.repository';
import { UserDevice } from './entities/user-device.entity';
import { NatsService } from '../nats/nats.service';
import { SUBJECTS } from '../nats/nats.subject';
import { RealUserRepository } from './repositories/real-user.repository';
import { RealUser } from './entities/real-user.entity';
import { UserLoggedInEvent, UserSyncEvent } from '../nats/nats.event';
import { SettingsService } from '../settings/setting.service';
import { UserEmbeddedWallet } from './entities/user-embedded-wallet.entity';
import { UserEmbeddedWalletRepository } from './repositories/user-embedded-wallet.repository';
import { EmbeddedWalletDto, UpdateEmbeddedWalletNameInputDTO } from './dto/embedded-wallet.dto';
import { generateReferrerCode } from './repositories/utils';
import { WALLET_NOT_FOUND, INVALID_WALLET_NAME } from 'libs/common/api-errors/errors';
import { WALLET_NAME_MIN_LENGTH, WALLET_NAME_MAX_LENGTH, WALLET_NAME_REGEX } from './user.constants';
import { providers } from 'tronweb';

@Injectable()
export class UsersService {
    constructor(
        private readonly userRepository: UserRepository,
        private readonly userManagedWalletRepository: UserManagedWalletRepository,
        private readonly userPerpetualStatusRepository: UserPerpetualStatusRepository,
        private readonly perpetualAgentRepository: PerpetualAgentRepository,
        private readonly em: EntityManager,
        private readonly walletService: WalletService,
        private readonly userDeviceRepository: UserDeviceRepository,
        private readonly activityLogRepository: ActivityLogRepository,
        private readonly realUserRepository: RealUserRepository,
        private readonly settingsService: SettingsService,
        private readonly natsService: NatsService,
        private readonly userEmbeddedWalletRepository: UserEmbeddedWalletRepository,
    ) {}

    async getUserById(id: string): Promise<Loaded<User, never, '*', never> | null> {
        return await this.userRepository.findOne(id);
    }

    async getOrCreateUserByWallet(
        walletAddress: string,
        authProvider: AuthProvider,
        referrerCode?: string,
        fingerprint?: string,
        chainType?: ChainType,
    ): Promise<User> {
        return await this.em.fork().transactional(
            async (txEm) => {
                const { user, isNewUser } = await this.userRepository.getOrCreateUser(
                    txEm,
                    walletAddress,
                    authProvider,
                );

                await this.createDefaultSettings(txEm, user);

                let device: UserDevice | undefined;
                if (fingerprint) {
                    device = await this.userDeviceRepository.getOrCreateUserDevice(txEm, user, fingerprint);
                }

                const { realUser, isNewRealUser } = await this.realUserRepository.getOrCreateRealUser(
                    txEm,
                    user,
                    device,
                );

                const activity = {
                    userId: user.id,
                    activity: isNewUser ? 'user_created' : 'user_logged_in',
                    description: isNewUser ? 'Create a new web3 account' : 'Login to web3 account',
                    ...(fingerprint ? { fingerprint } : {}),
                    ...(referrerCode ? { referrerCode } : {}),
                    createdAt: new Date(),
                };
                this.activityLogRepository.createActivityLog(txEm, user, JSON.stringify(activity), device);
                if (isNewRealUser) {
                    const activity = {
                        userId: realUser.id,
                        activity: 'real_user_created',
                        description: 'Create a new real user',
                        ...(fingerprint ? { fingerprint } : {}),
                        ...(referrerCode ? { referrerCode } : {}),
                        createdAt: new Date(),
                    };
                    this.activityLogRepository.createActivityLog(txEm, user, JSON.stringify(activity), device);
                }

                if (chainType == ChainType.EVM) {
                    this.publishToUserSyncService(user, walletAddress);
                }
                this.publishToAffliateService(realUser, user, isNewUser, fingerprint, referrerCode);
                await txEm.commit();
                return user;
            },
            { ignoreNestedTransactions: true },
        );
    }

    publishToUserSyncService(user: User, walletAddress: string) {
        this.natsService.publish<UserSyncEvent>(SUBJECTS.USER_SYNC_INFO, {
            userId: user.id,
            walletAddress: walletAddress,
        });
    }

    publishToAffliateService(
        realUser: RealUser,
        user: User,
        isNewUser: boolean,
        fingerprint?: string,
        referrerCode?: string,
    ) {
        this.natsService.publish<UserLoggedInEvent>(SUBJECTS.USER_LOGGED_IN, {
            realUserId: realUser.id,
            userId: user.id,
            userReferrerCode: user.referrerCode,
            isNewUser,
            fingerprint,
            referrerCode,
        });
    }

    async getOrCreateUserByTelegramAccount(userDto: TelegramUserDTO): Promise<{ user: User; isNewUser: boolean }> {
        return await this.em.fork().transactional(
            async (txEm) => {
                const { user, isNewUser } = await this.userRepository.getOrCreateUserByTelegramAccount(txEm, userDto);
                if (isNewUser) {
                    await this.createUserManagedWallet(txEm, user);
                }
                await this.createDefaultSettings(txEm, user);

                let device: UserDevice | undefined;
                if (userDto.fingerprint) {
                    device = await this.userDeviceRepository.getOrCreateUserDevice(txEm, user, userDto.fingerprint);
                }

                const { realUser, isNewRealUser } = await this.realUserRepository.getOrCreateRealUser(
                    txEm,
                    user,
                    device,
                );

                const activity = {
                    userId: user.id,
                    activity: isNewUser ? 'user_created' : 'user_logged_in',
                    description: isNewUser ? 'Create a new telegram account' : 'Login to telegram account',
                    ...(userDto.fingerprint ? { fingerprint: userDto.fingerprint } : {}),
                    ...(userDto.referrerCode ? { referrerCode: userDto.referrerCode } : {}),
                    createdAt: new Date(),
                };
                this.activityLogRepository.createActivityLog(txEm, user, JSON.stringify(activity), device);

                const wallets = await this.userManagedWalletRepository.findAll({
                    where: { user },
                    populate: ['encryptedPrivateKey'],
                });

                // fallback for ARB wallet, remove when stable
                const arbWallet = wallets.find((w) => w.chain === ChainType.ARB);
                if (!arbWallet) {
                    const evmWallet = wallets.find((w) => w.chain === ChainType.EVM);
                    if (evmWallet) {
                        await this.createUserWalletFallback(
                            user,
                            evmWallet.walletAddress,
                            evmWallet.encryptedPrivateKey,
                            ChainType.ARB,
                        );
                    }
                }

                if (isNewRealUser) {
                    const activity = {
                        userId: realUser.id,
                        activity: 'real_user_created',
                        description: 'Create a new real user',
                        ...(userDto.fingerprint ? { fingerprint: userDto.fingerprint } : {}),
                        ...(userDto.referrerCode ? { referrerCode: userDto.referrerCode } : {}),
                        createdAt: new Date(),
                    };
                    this.activityLogRepository.createActivityLog(txEm, user, JSON.stringify(activity), device);
                }

                // this.publishToAffliateService(realUser, user, isNewUser, userDto.fingerprint, userDto.referrerCode);

                await txEm.commit();
                return { user: user, isNewUser: isNewUser };
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getOrCreateUserByGoogleAccount(data: {
        id: string;
        email: string;
        name: string;
        picture: string;
        fingerprint?: string;
        referrerCode?: string;
    }): Promise<{ user: User; isNewUser: boolean }> {
        return await this.em.fork().transactional(
            async (txEm) => {
                const existingUser = await txEm.findOne(User, {
                    $or: [{ googleId: data.id }, { email: data.email }],
                });

                if (existingUser) {
                    if (!existingUser.googleId) {
                        existingUser.googleId = data.id;
                        existingUser.email = data.email;
                        existingUser.name = data.name;
                        existingUser.avatar = data.picture;
                        if (!existingUser.authProvider) {
                            existingUser.authProvider = AuthProvider.GOOGLE;
                        }
                        await txEm.persistAndFlush(existingUser);
                    }

                    // For existing users logging in, set isFirstLogin to false
                    if (existingUser.isFirstLogin) {
                        existingUser.isFirstLogin = false;
                        await txEm.persistAndFlush(existingUser);
                    }

                    // Handle fingerprint for existing user
                    let device: UserDevice | undefined;
                    if (data.fingerprint) {
                        device = await this.userDeviceRepository.getOrCreateUserDevice(
                            txEm,
                            existingUser,
                            data.fingerprint,
                        );
                    }

                    // Update real user association
                    await this.realUserRepository.getOrCreateRealUser(txEm, existingUser, device);

                    return { user: existingUser, isNewUser: false };
                }

                const newUser = new User();
                newUser.googleId = data.id;
                newUser.email = data.email;
                newUser.name = data.name;
                newUser.avatar = data.picture;
                newUser.authProvider = AuthProvider.GOOGLE;
                // Generate referrer code for new user
                newUser.referrerCode = generateReferrerCode();
                newUser.createdAt = new Date();
                newUser.isFirstLogin = true; // Set to true for new users

                // First persist the user to get the ID
                await txEm.persistAndFlush(newUser);

                // Then create default settings after user is persisted
                await this.createDefaultSettings(txEm, newUser);

                let device: UserDevice | undefined;
                if (data.fingerprint) {
                    device = await this.userDeviceRepository.getOrCreateUserDevice(txEm, newUser, data.fingerprint);
                }

                await this.realUserRepository.getOrCreateRealUser(txEm, newUser, device);

                // Note: Referrer handling is done at the application level, not at the entity level
                // The referrerCode is stored in the User entity for tracking purposes

                return { user: newUser, isNewUser: true };
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getOrCreateUserByEmail(data: {
        email: string;
        referrerCode?: string;
        fingerprint?: string;
        provider?: AuthProvider;
    }): Promise<{ user: User; isNewUser: boolean }> {
        return await this.em.fork().transactional(
            async (txEm) => {
                const existingUser = await txEm.findOne(User, { email: data.email });

                if (existingUser) {
                    // For existing users logging in, set isFirstLogin to false
                    if (existingUser.isFirstLogin) {
                        existingUser.isFirstLogin = false;
                        await txEm.persistAndFlush(existingUser);
                    }
                    return { user: existingUser, isNewUser: false };
                }

                const newUser = new User();
                newUser.email = data.email;
                newUser.authProvider = data.provider ? data.provider : AuthProvider.EMAIL;
                newUser.referrerCode = generateReferrerCode();
                newUser.createdAt = new Date();
                newUser.isFirstLogin = true; // Set to true for new users

                // First persist the user to get the ID
                await txEm.persistAndFlush(newUser);

                // Then create default settings after user is persisted
                await this.createDefaultSettings(txEm, newUser);

                let device: UserDevice | undefined;
                if (data.fingerprint) {
                    device = await this.userDeviceRepository.getOrCreateUserDevice(txEm, newUser, data.fingerprint);
                }

                await this.realUserRepository.getOrCreateRealUser(txEm, newUser, device);

                // Note: Referrer handling is done at the application level, not at the entity level
                // The referrerCode is stored in the User entity for tracking purposes

                return { user: newUser, isNewUser: true };
            },
            { ignoreNestedTransactions: true },
        );
    }

    async createUserManagedWallet(txEm: EntityManager, user: User): Promise<void> {
        const walletGenerator = await this.walletService.generateWallets(
            [ChainType.EVM, ChainType.TRON, ChainType.SOLANA].join(','),
        );
        if (Array.isArray(walletGenerator) && walletGenerator.length > 0) {
            const wallets = walletGenerator.map((i) => {
                return {
                    walletAddress: i.address,
                    encryptedPrivateKey: i.encrypt_private_key,
                    chain: i.chain_type as ChainType,
                };
            });

            // Duplicate EVM wallet to ARB
            const arbWallet = wallets.find((w) => w.chain === ChainType.EVM);
            if (arbWallet) {
                wallets.push({
                    ...arbWallet,
                    chain: ChainType.ARB,
                });
            }

            await this.userManagedWalletRepository.createWalletsForUser(txEm, user, wallets);
        }
    }

    async createDefaultSettings(txEm: EntityManager, user: User): Promise<void> {
        await this.settingsService.createDefaultSettings(txEm, user);
    }

    // todo: remove fallback
    async createUserWalletFallback(
        user: User,
        walletAddress: string,
        encryptedPrivateKey: string,
        chain: ChainType,
    ): Promise<UserManagedWallet[]> {
        return this.em.fork().transactional(
            async (txEm) => {
                const wallets = await this.userManagedWalletRepository.createWalletsForUser(txEm, user, [
                    {
                        walletAddress: walletAddress,
                        chain: chain,
                        encryptedPrivateKey,
                    },
                ]);

                return wallets;
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getUser(id: string): Promise<User> {
        const user = await this.userRepository.findOne({ id: id });
        if (!user) {
            throw new ApiError(USER_NOT_FOUND_ERROR);
        }
        return user;
    }

    async getBalance(chain: ChainType, walletAddress: string): Promise<number> {
        switch (chain) {
            case ChainType.EVM:
                return getEvmBalance(walletAddress);
            case ChainType.SOLANA:
                return getSolanaBalance(walletAddress);
            case ChainType.TRON:
                return getTronBalance(walletAddress);
        }

        return 0;
    }

    async getUserManagedWallets(user: User) {
        await this.em.fork().populate(user, ['userManagedWallets']);
        const wallets = user.userManagedWallets as any;
        for (let i = 0; i < wallets.length; i++) {
            wallets[i].balance = await this.getBalance(wallets[i].chain, wallets[i].walletAddress);
        }
        return wallets;
    }

    async updateUser(user: User) {
        await this.userRepository.updateUser(user);
    }

    async updateUserFirstLoginStatus(userId: string, isFirstLogin: boolean): Promise<void> {
        const user = await this.getUserById(userId);
        if (!user) {
            throw new ApiError(USER_NOT_FOUND_ERROR);
        }
        user.isFirstLogin = isFirstLogin;
        await this.updateUser(user);
    }

    async updateUserLanguage(user_id: string, _language: string) {
        return this.userRepository.updateUserLanguage(user_id, _language);
    }

    async getPerpetualStatus(user: User) {
        return this.userPerpetualStatusRepository.findOne({ user });
    }

    async updatePerpetualStatus(user: User, perpetualStatus: UserPerpetualStatusDTO) {
        return this.em.fork().transactional(
            async (txEm) => {
                return this.userPerpetualStatusRepository.createOrUpdateStatus(txEm, user, perpetualStatus);
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getUserManageWalletsWithPrivateKey(user: User) {
        return this.userManagedWalletRepository.findAll({
            where: { user },
            populate: ['encryptedPrivateKey'],
        });
    }

    async getPerpetualAgentByUser(user: User) {
        return this.perpetualAgentRepository.findOne(
            {
                user,
            },
            { populate: ['encryptedPrivateKey'] },
        );
    }

    async createPerpetualAgent(user: User, agent: PerpetualAgentDto) {
        return this.em.fork().transactional(
            async (txEm) => {
                return this.perpetualAgentRepository.createAgentForUser(txEm, user, agent);
            },
            { ignoreNestedTransactions: true },
        );
    }

    async updatePerpetualAgent(user: User, agent: UpdatePerpetualAgentDto) {
        return this.em.fork().transactional(
            async (txEm) => {
                return this.perpetualAgentRepository.updateAgentForUser(txEm, user, agent);
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getUserInfoByTelegram(request: GetUserInfoRequest): Promise<UserInfoResponse | null> {
        return await this.em.fork().transactional(
            async (txEm) => {
                const user = await this.userRepository.findOneUser(txEm, request.telegramId, request.userId);
                if (!user) throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);

                return {
                    id: user.id,
                    name: user.name,
                    telegramChatId: user.telegramChatId ?? 0,
                    telegramId: user.telegramId ?? '',
                    telegramUsername: user.telegramUsername,
                    language: user.language,
                };
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getUserByTelegramId(telegramId: string): Promise<User | null> {
        return this.userRepository.findOne({
            telegramId: telegramId,
        });
    }

    async signEvmTransaction(request: SignUserTransactionEvmRequest): Promise<SignUserTransactionEvmResponse> {
        const user = await this.em
            .fork({
                disableTransactions: true,
            })
            .transactional(
                async (txEm) => {
                    return txEm.findOne(User, request.userId);
                },
                { ignoreNestedTransactions: true },
            );

        if (!user) {
            throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);
        }

        let chainType = ChainType.EVM;
        switch (request.chain) {
            case RequestChainType.EVM:
                chainType = ChainType.EVM;
                break;
            case RequestChainType.ARB:
                chainType = ChainType.ARB;
                break;
            case RequestChainType.SOLANA:
                chainType = ChainType.SOLANA;
                break;
            case RequestChainType.TRON:
                chainType = ChainType.TRON;
                break;
            default:
                break;
        }

        const wallets = await this.getUserManageWalletsWithPrivateKey(user);
        const walletWithPrivateKey = wallets.find((w) => w.chain === chainType);

        if (!walletWithPrivateKey) {
            throw new RpcException(GRPC_EXCEPTION.USER_WALLET_NOT_FOUND);
        }

        if (walletWithPrivateKey.walletAddress !== request.from) {
            throw new RpcException(GRPC_EXCEPTION.USER_WALLET_NOT_FOUND);
        }

        const tx = new Transaction();
        tx.data = request.data;
        tx.to = request.to;
        tx.value = request.value ? parseEther(request.value.toString()) : BigInt(0);
        tx.maxFeePerGas = request.maxFeePerGas ? BigInt(request.maxFeePerGas) : null;
        tx.gasPrice = request.gasPrice ? BigInt(request.gasPrice) : null;
        tx.maxPriorityFeePerGas = request.maxPriorityFeePerGas ? BigInt(request.maxPriorityFeePerGas) : null;
        tx.gasPrice = request.gasPrice ? BigInt(request.gasPrice) : null;

        if (request.gasLimit) {
            tx.gasLimit = BigInt(request.gasLimit);
        }

        const signature = await this.walletService.signHashed(walletWithPrivateKey, tx.unsignedHash);

        if (!signature) {
            throw new RpcException(GRPC_EXCEPTION.INTERNAL_SERVER_ERROR);
        }

        return {
            ...request,
            signedTransaction: signature,
        };
    }

    async getUserReferrerCode(userId: string): Promise<GetUserReferrerCodeResponse> {
        const user = await this.userRepository.findOne({ id: userId });
        if (!user) {
            throw new RpcException(GRPC_EXCEPTION.USER_NOT_FOUND);
        }

        return {
            referrerCode: user.referrerCode ?? '',
        };
    }

    async createUserEmbeddedWallets(user: User, wallets: EmbeddedWalletDto[]): Promise<UserEmbeddedWallet[]> {
        return this.em.fork().transactional(
            async (txEm) => {
                return this.userEmbeddedWalletRepository.createWalletsForUser(txEm, user, wallets);
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getUserEmbeddedWallets(user: User) {
        await this.em.fork().populate(user, ['userEmbeddedWallets']);
        const wallets = user.userEmbeddedWallets as any;
        for (let i = 0; i < wallets.length; i++) {
            wallets[i].balance = await this.getBalance(wallets[i].chain, wallets[i].walletAddress);
        }

        return wallets;
    }

    async getUserEmbeddedWalletsWithoutBalance(user: User) {
        await this.em.fork().populate(user, ['userEmbeddedWallets']);
        return user.userEmbeddedWallets;
    }

    async getUserBySubOrg(subOrg: string): Promise<Loaded<User, never, '*', never> | null> {
        return await this.userRepository.findOne({
            subOrgId: subOrg,
        });
    }

    async updateEmbeddedWalletName(
        userId: string,
        input: UpdateEmbeddedWalletNameInputDTO,
    ): Promise<UserEmbeddedWallet> {
        return this.em.fork().transactional(
            async (txEm) => {
                // Get the user
                const user = await this.getUserById(userId);
                if (!user) {
                    throw new ApiError(USER_NOT_FOUND_ERROR);
                }

                // Validate wallet name format (additional server-side validation)
                if (
                    !WALLET_NAME_REGEX.test(input.name) ||
                    input.name.length < WALLET_NAME_MIN_LENGTH ||
                    input.name.length > WALLET_NAME_MAX_LENGTH
                ) {
                    throw new ApiError(INVALID_WALLET_NAME);
                }

                const wallets = await this.getUserEmbeddedWalletsWithoutBalance(user);

                const wallet = wallets.find((w) => w.id === input.id);

                if (!wallet) {
                    throw new ApiError(WALLET_NOT_FOUND);
                }

                // Update the wallet name
                const updatedWallet = await this.userEmbeddedWalletRepository.updateWalletName(
                    wallet,
                    txEm,
                    user,
                    input.name.trim(),
                );

                return updatedWallet;
            },
            { ignoreNestedTransactions: true },
        );
    }

    async getUserByEmail(email: string): Promise<Loaded<User, never, '*', never> | null> {
        return await this.userRepository.findOne({
            email,
        });
    }
}
